<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Enterprise Sidebar - Tailwind CSS</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-72 bg-slate-900 shadow-xl">
        <!-- Header -->
        <div class="flex items-center h-20 px-6 bg-slate-800 border-b border-slate-700">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-lg font-semibold text-white">Enterprise</h1>
                    <p class="text-xs text-slate-400">Management Suite</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mt-6 px-4">
            <!-- Main Section -->
            <div class="mb-8">
                <p class="px-3 text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3">Main</p>
                <div class="space-y-1">
                    <!-- Dashboard -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-white bg-blue-600 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        <span class="font-medium">Dashboard</span>
                    </a>

                    <!-- Analytics -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span class="font-medium">Analytics</span>
                        <span class="ml-auto bg-slate-700 text-slate-300 text-xs font-medium px-2 py-1 rounded">24</span>
                    </a>

                    <!-- Reports -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="font-medium">Reports</span>
                    </a>
                </div>
            </div>

            <!-- Management Section -->
            <div class="mb-8">
                <p class="px-3 text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3">Management</p>
                <div class="space-y-1">
                    <!-- Users -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <span class="font-medium">Users</span>
                        <span class="ml-auto bg-green-600 text-white text-xs font-medium px-2 py-1 rounded">1.2k</span>
                    </a>

                    <!-- Departments -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <span class="font-medium">Departments</span>
                        <span class="ml-auto bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded">12</span>
                    </a>

                    <!-- Projects -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        <span class="font-medium">Projects</span>
                        <span class="ml-auto bg-purple-600 text-white text-xs font-medium px-2 py-1 rounded">45</span>
                    </a>

                    <!-- Resources -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="font-medium">Resources</span>
                    </a>
                </div>
            </div>

            <!-- Communication Section -->
            <div class="mb-8">
                <p class="px-3 text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3">Communication</p>
                <div class="space-y-1">
                    <!-- Messages -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <span class="font-medium">Messages</span>
                        <span class="ml-auto bg-red-600 text-white text-xs font-medium px-2 py-1 rounded">8</span>
                    </a>

                    <!-- Notifications -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 17H7l4 4v-4zM13 3h5l-5-5v5zM11 3H7l4-4v4z"></path>
                        </svg>
                        <span class="font-medium">Notifications</span>
                        <span class="ml-auto bg-orange-600 text-white text-xs font-medium px-2 py-1 rounded">15</span>
                    </a>

                    <!-- Calendar -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-medium">Calendar</span>
                    </a>
                </div>
            </div>

            <!-- System Section -->
            <div>
                <p class="px-3 text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3">System</p>
                <div class="space-y-1">
                    <!-- Settings -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="font-medium">Settings</span>
                    </a>

                    <!-- Security -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span class="font-medium">Security</span>
                    </a>

                    <!-- Support -->
                    <a href="#" class="flex items-center px-3 py-2.5 text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg group transition-colors duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium">Support</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- User Profile -->
        <div class="absolute bottom-0 left-0 right-0 p-4 bg-slate-800 border-t border-slate-700">
            <div class="flex items-center space-x-3">
                <img class="w-10 h-10 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="User avatar">
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-white truncate">Michael Chen</p>
                    <p class="text-xs text-slate-400 truncate">System Administrator</p>
                </div>
                <button class="text-slate-400 hover:text-white transition-colors duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-72 p-8">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">Professional Enterprise Sidebar</h1>
            <p class="text-lg text-gray-600 leading-relaxed">
                This enterprise-grade sidebar is designed for professional business applications with organized 
                navigation sections, comprehensive functionality, and a clean, corporate aesthetic. Perfect for 
                admin dashboards, business management systems, and enterprise software solutions.
            </p>
        </div>
    </div>
</body>
</html>
