<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Colorful Gradient Sidebar - Tailwind CSS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'gradient': 'gradient 8s ease infinite',
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        gradient: {
                            '0%, 100%': {
                                'background-size': '200% 200%',
                                'background-position': 'left center'
                            },
                            '50%': {
                                'background-size': '200% 200%',
                                'background-position': 'right center'
                            },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100 font-sans">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-80 bg-gradient-to-br from-purple-600 via-pink-600 to-blue-600 animate-gradient shadow-2xl">
        <!-- Decorative Elements -->
        <div class="absolute top-10 right-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-float"></div>
        <div class="absolute bottom-20 left-10 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-float" style="animation-delay: -3s;"></div>
        
        <!-- Header -->
        <div class="relative p-8 text-center">
            <div class="w-20 h-20 mx-auto mb-4 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <span class="text-3xl">🌈</span>
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">ColorSpace</h1>
            <p class="text-purple-100 text-sm">Creative Dashboard</p>
        </div>

        <!-- Quick Stats -->
        <div class="px-6 mb-6">
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-4 text-center">
                    <div class="text-2xl font-bold text-white">24</div>
                    <div class="text-xs text-purple-100">Projects</div>
                </div>
                <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-4 text-center">
                    <div class="text-2xl font-bold text-white">156</div>
                    <div class="text-xs text-purple-100">Tasks</div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="px-6">
            <div class="space-y-2">
                <!-- Dashboard -->
                <a href="#" class="flex items-center px-4 py-4 text-white bg-white bg-opacity-20 backdrop-blur-sm rounded-xl group hover:bg-opacity-30 transition-all duration-300 transform hover:scale-105">
                    <div class="w-8 h-8 mr-4 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                    </div>
                    <span class="font-semibold">Dashboard</span>
                    <div class="ml-auto w-2 h-2 bg-yellow-300 rounded-full animate-pulse"></div>
                </a>

                <!-- Creative Projects -->
                <a href="#" class="flex items-center px-4 py-4 text-purple-100 rounded-xl group hover:bg-white hover:bg-opacity-10 hover:text-white transition-all duration-300">
                    <div class="w-8 h-8 mr-4 bg-gradient-to-br from-pink-400 to-purple-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Creative Projects</span>
                    <span class="ml-auto bg-gradient-to-r from-pink-400 to-purple-500 text-white text-xs font-bold px-2 py-1 rounded-full">8</span>
                </a>

                <!-- Design Assets -->
                <a href="#" class="flex items-center px-4 py-4 text-purple-100 rounded-xl group hover:bg-white hover:bg-opacity-10 hover:text-white transition-all duration-300">
                    <div class="w-8 h-8 mr-4 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Design Assets</span>
                    <span class="ml-auto bg-gradient-to-r from-blue-400 to-cyan-500 text-white text-xs font-bold px-2 py-1 rounded-full">24</span>
                </a>

                <!-- Team Collaboration -->
                <a href="#" class="flex items-center px-4 py-4 text-purple-100 rounded-xl group hover:bg-white hover:bg-opacity-10 hover:text-white transition-all duration-300">
                    <div class="w-8 h-8 mr-4 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Team Collaboration</span>
                    <span class="ml-auto bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-bold px-2 py-1 rounded-full">12</span>
                </a>

                <!-- Analytics -->
                <a href="#" class="flex items-center px-4 py-4 text-purple-100 rounded-xl group hover:bg-white hover:bg-opacity-10 hover:text-white transition-all duration-300">
                    <div class="w-8 h-8 mr-4 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Analytics</span>
                </a>

                <!-- Messages -->
                <a href="#" class="flex items-center px-4 py-4 text-purple-100 rounded-xl group hover:bg-white hover:bg-opacity-10 hover:text-white transition-all duration-300">
                    <div class="w-8 h-8 mr-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Messages</span>
                    <span class="ml-auto bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">5</span>
                </a>
            </div>

            <!-- Divider -->
            <div class="my-6 border-t border-white border-opacity-20"></div>

            <!-- Secondary Navigation -->
            <div class="space-y-2">
                <a href="#" class="flex items-center px-4 py-3 text-purple-100 rounded-xl group hover:bg-white hover:bg-opacity-10 hover:text-white transition-all duration-300">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Settings</span>
                </a>

                <a href="#" class="flex items-center px-4 py-3 text-purple-100 rounded-xl group hover:bg-white hover:bg-opacity-10 hover:text-white transition-all duration-300">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Help Center</span>
                </a>
            </div>
        </nav>

        <!-- User Profile -->
        <div class="absolute bottom-0 left-0 right-0 p-6">
            <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-4">
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <img class="w-12 h-12 rounded-full border-2 border-white" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="User avatar">
                        <div class="absolute bottom-0 right-0 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-semibold text-white truncate">Sarah Wilson</p>
                        <p class="text-xs text-purple-100 truncate">Creative Director</p>
                    </div>
                    <button class="text-white hover:text-purple-200 transition-colors duration-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-80 p-8">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">Colorful Gradient Sidebar</h1>
            <p class="text-lg text-gray-600 leading-relaxed">
                This vibrant sidebar features animated gradients, floating elements, and colorful design elements. 
                Perfect for creative applications, design tools, or any interface that wants to convey energy, 
                creativity, and modern aesthetics with smooth animations and engaging visual effects.
            </p>
        </div>
    </div>
</body>
</html>
