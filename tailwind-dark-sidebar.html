<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Theme Sidebar - Tailwind CSS</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 font-sans">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-80 bg-gray-800 shadow-2xl transform transition-transform duration-300 ease-in-out">
        <!-- Header -->
        <div class="flex items-center justify-center h-24 bg-gradient-to-r from-purple-600 to-blue-600">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                    <span class="text-2xl">⚡</span>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-white">DarkSpace</h1>
                    <p class="text-xs text-purple-200">Admin Panel</p>
                </div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="p-4">
            <div class="relative">
                <input type="text" placeholder="Search..." class="w-full bg-gray-700 text-white placeholder-gray-400 rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-gray-600 transition-all duration-200">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="px-4 pb-4">
            <div class="space-y-1">
                <!-- Dashboard -->
                <a href="#" class="flex items-center px-4 py-3 text-white bg-purple-600 rounded-lg group transition-all duration-200 hover:bg-purple-700">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Dashboard</span>
                    <div class="ml-auto w-2 h-2 bg-white rounded-full"></div>
                </a>

                <!-- Analytics -->
                <a href="#" class="flex items-center px-4 py-3 text-gray-300 rounded-lg group hover:bg-gray-700 hover:text-white transition-all duration-200">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Analytics</span>
                    <span class="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">24</span>
                </a>

                <!-- Users -->
                <a href="#" class="flex items-center px-4 py-3 text-gray-300 rounded-lg group hover:bg-gray-700 hover:text-white transition-all duration-200">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Users</span>
                    <span class="ml-auto bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">156</span>
                </a>

                <!-- Projects -->
                <a href="#" class="flex items-center px-4 py-3 text-gray-300 rounded-lg group hover:bg-gray-700 hover:text-white transition-all duration-200">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Projects</span>
                    <div class="ml-auto flex items-center space-x-2">
                        <span class="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full">8</span>
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </a>

                <!-- Messages -->
                <a href="#" class="flex items-center px-4 py-3 text-gray-300 rounded-lg group hover:bg-gray-700 hover:text-white transition-all duration-200">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Messages</span>
                    <span class="ml-auto bg-yellow-500 text-black text-xs font-bold px-2 py-1 rounded-full">7</span>
                </a>

                <!-- Files -->
                <a href="#" class="flex items-center px-4 py-3 text-gray-300 rounded-lg group hover:bg-gray-700 hover:text-white transition-all duration-200">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Files</span>
                </a>
            </div>

            <!-- Divider -->
            <div class="my-6 border-t border-gray-700"></div>

            <!-- Secondary Navigation -->
            <div class="space-y-1">
                <p class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">Tools</p>
                
                <a href="#" class="flex items-center px-4 py-3 text-gray-300 rounded-lg group hover:bg-gray-700 hover:text-white transition-all duration-200">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Settings</span>
                </a>

                <a href="#" class="flex items-center px-4 py-3 text-gray-300 rounded-lg group hover:bg-gray-700 hover:text-white transition-all duration-200">
                    <div class="w-6 h-6 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Help</span>
                </a>
            </div>
        </nav>

        <!-- User Profile -->
        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gray-900 border-t border-gray-700">
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <img class="w-12 h-12 rounded-full border-2 border-purple-500" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="User avatar">
                    <div class="absolute bottom-0 right-0 w-4 h-4 bg-green-400 border-2 border-gray-900 rounded-full"></div>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-white truncate">Alex Johnson</p>
                    <p class="text-xs text-gray-400 truncate">Senior Developer</p>
                </div>
                <button class="text-gray-400 hover:text-white transition-colors duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-80 p-8">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-white mb-6">Dark Theme Sidebar</h1>
            <p class="text-lg text-gray-300 leading-relaxed">
                This dark-themed sidebar features a sophisticated design with gradient headers, search functionality, 
                notification badges, and smooth hover animations. Perfect for modern admin dashboards and applications 
                that require a professional dark interface.
            </p>
        </div>
    </div>
</body>
</html>
