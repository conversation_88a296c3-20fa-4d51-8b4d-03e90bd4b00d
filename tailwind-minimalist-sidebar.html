<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimalist Sidebar - Tailwind CSS</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-20 hover:w-64 bg-white shadow-lg border-r border-gray-100 transition-all duration-300 ease-in-out group">
        <!-- Header -->
        <div class="flex items-center justify-center h-16 border-b border-gray-100">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <span class="text-white font-bold text-lg">M</span>
            </div>
            <div class="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <h1 class="text-lg font-semibold text-gray-900">Minimal</h1>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mt-8">
            <div class="space-y-2 px-3">
                <!-- Dashboard -->
                <a href="#" class="flex items-center px-3 py-3 text-blue-600 bg-blue-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Dashboard</span>
                </a>

                <!-- Analytics -->
                <a href="#" class="flex items-center px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Analytics</span>
                    <span class="ml-auto bg-red-100 text-red-600 text-xs font-semibold px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">12</span>
                </a>

                <!-- Users -->
                <a href="#" class="flex items-center px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Users</span>
                </a>

                <!-- Projects -->
                <a href="#" class="flex items-center px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Projects</span>
                    <span class="ml-auto bg-green-100 text-green-600 text-xs font-semibold px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">5</span>
                </a>

                <!-- Messages -->
                <a href="#" class="flex items-center px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Messages</span>
                    <span class="ml-auto bg-blue-100 text-blue-600 text-xs font-semibold px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">3</span>
                </a>

                <!-- Calendar -->
                <a href="#" class="flex items-center px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Calendar</span>
                </a>

                <!-- Files -->
                <a href="#" class="flex items-center px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Files</span>
                </a>
            </div>

            <!-- Divider -->
            <div class="my-6 mx-3 border-t border-gray-200"></div>

            <!-- Secondary Navigation -->
            <div class="space-y-2 px-3">
                <a href="#" class="flex items-center px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Settings</span>
                </a>

                <a href="#" class="flex items-center px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl group-hover:px-4 transition-all duration-200">
                    <div class="w-6 h-6 flex-shrink-0">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <span class="ml-4 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">Help</span>
                </a>
            </div>
        </nav>

        <!-- User Profile -->
        <div class="absolute bottom-0 left-0 right-0 p-3 border-t border-gray-100">
            <div class="flex items-center justify-center group-hover:justify-start transition-all duration-300">
                <img class="w-10 h-10 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="User avatar">
                <div class="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <p class="text-sm font-medium text-gray-900">John Doe</p>
                    <p class="text-xs text-gray-500">Designer</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-20 p-8">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">Minimalist Sidebar</h1>
            <p class="text-lg text-gray-600 leading-relaxed">
                This minimalist sidebar starts collapsed and expands on hover, revealing navigation labels and badges. 
                The design emphasizes clean lines, subtle animations, and efficient use of space while maintaining 
                full functionality and accessibility.
            </p>
        </div>
    </div>
</body>
</html>
